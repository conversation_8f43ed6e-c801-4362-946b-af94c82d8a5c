<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title>ONLYOFFICE Editor</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <script type="text/javascript" th:src="${documentServerApiUrl}"></script>
    <style>
        html {
            height: 100%;
            width: 100%;
        }

        body {
            background: #fff;
            color: #333;
            font-family: Arial, Tahoma,sans-serif;
            font-size: 12px;
            font-weight: normal;
            height: 100%;
            margin: 0;
            overflow-y: hidden;
            padding: 0;
            text-decoration: none;
        }

        .editor-window {
            height: 100%;
        }

        div {
            margin: 0;
            padding: 0;
        }
    </style>
</head>

<body>
    <div class="editor-window">
        <div id="editor"></div>
    </div>
</body>

<script th:inline="javascript">
    const config = [[${config}]];
    var docEditor = new DocsAPI.DocEditor("editor", config);
</script>
</html>