/**
 *
 * (c) Copyright Ascensio System SIA 2025
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.onlyoffice.demoexample.controllers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.onlyoffice.context.DocsIntegrationSdkContext;
import com.onlyoffice.manager.url.UrlManager;
import com.onlyoffice.model.documenteditor.Config;
import com.onlyoffice.model.documenteditor.config.document.Type;
import com.onlyoffice.model.documenteditor.config.editorconfig.Mode;
import com.onlyoffice.service.documenteditor.config.ConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class MainController {

    @Autowired
    private DocsIntegrationSdkContext docsIntegrationSdk;

    @GetMapping("/editor")
    public String main(final Model model) throws JsonProcessingException {
        ConfigService configService = docsIntegrationSdk.getConfigService();
        UrlManager urlManager = docsIntegrationSdk.getUrlManager();

        Config config = configService.createConfig("1", Mode.EDIT, Type.DESKTOP);
        config.getEditorConfig().setLang("zh");

        model.addAttribute("config", config);
        model.addAttribute("documentServerApiUrl", urlManager.getDocumentServerApiUrl());

        return "editor";
    }
}
